#!/usr/bin/env python3
"""
Hinglish Voice Assistant for macOS
A voice assistant that speaks in Hinglish (Hindi-English mix)
"""

import speech_recognition as sr
import subprocess
import random
import time
import threading
from datetime import datetime

class HinglishVoiceAssistant:
    def __init__(self):
        self.recognizer = sr.Recognizer()
        self.microphone = sr.Microphone()
        self.is_listening = False
        
        # Hinglish responses for different scenarios
        self.greetings = [
            "Namaste! Main aapka voice assistant hun. <PERSON><PERSON> help kar sakta hun?",
            "Hello ji! Aap kya chahte hain?",
            "Adaab! Main yahan hun aapki madad ke liye.",
            "Sat sri akal! Batayiye kya kaam hai?"
        ]
        
        self.time_responses = [
            "Abhi time hai",
            "Current time ye hai",
            "Samay hai",
            "Time check karte hain"
        ]
        
        self.weather_responses = [
            "Weather ke baare mein pata karne ke liye main abhi ready nahi hun, lekin aap weather app check kar sakte hain",
            "<PERSON><PERSON><PERSON> ki jankari ke liye weather application dekh lijiye",
            "Weather forecast ke liye online check kariye"
        ]
        
        self.goodbye_responses = [
            "Alvida! Phir milenge!",
            "Bye bye! Take care kijiye!",
            "Namaste! Acha laga baat karke!",
            "Tata! Khush rahiye!"
        ]
        
        self.unknown_responses = [
            "Sorry, samajh nahi aaya. Phir se boliye?",
            "Kya kaha aapne? Main samjha nahi.",
            "Thoda clear boliye, please.",
            "Ek baar aur try kariye."
        ]

    def speak(self, text):
        """Use macOS say command to speak text"""
        try:
            # Use macOS built-in text-to-speech
            subprocess.run(['say', text], check=True)
        except subprocess.CalledProcessError:
            print(f"Could not speak: {text}")

    def listen(self):
        """Listen for voice input"""
        try:
            with self.microphone as source:
                print("Listening... (Bol rahe hain)")
                self.recognizer.adjust_for_ambient_noise(source, duration=1)
                audio = self.recognizer.listen(source, timeout=5, phrase_time_limit=10)
            
            print("Processing... (Samajh raha hun)")
            text = self.recognizer.recognize_google(audio, language='en-IN')
            print(f"You said: {text}")
            return text.lower()
            
        except sr.WaitTimeoutError:
            return None
        except sr.UnknownValueError:
            self.speak("Kuch sunai nahi diya. Phir se boliye.")
            return None
        except sr.RequestError as e:
            self.speak("Internet connection ki problem hai.")
            print(f"Error: {e}")
            return None

    def get_current_time(self):
        """Get current time in Hinglish format"""
        now = datetime.now()
        time_str = now.strftime("%I:%M %p")
        response = random.choice(self.time_responses)
        return f"{response} {time_str}"

    def process_command(self, command):
        """Process voice commands and respond in Hinglish"""
        if not command:
            return
            
        # Greeting commands
        if any(word in command for word in ['hello', 'hi', 'namaste', 'hey']):
            response = random.choice(self.greetings)
            self.speak(response)
            
        # Time commands
        elif any(word in command for word in ['time', 'samay', 'kitna baja', 'what time']):
            time_response = self.get_current_time()
            self.speak(time_response)
            
        # Weather commands
        elif any(word in command for word in ['weather', 'mausam', 'temperature']):
            response = random.choice(self.weather_responses)
            self.speak(response)
            
        # Goodbye commands
        elif any(word in command for word in ['bye', 'goodbye', 'alvida', 'tata', 'exit', 'quit']):
            response = random.choice(self.goodbye_responses)
            self.speak(response)
            return False  # Signal to stop
            
        # Joke command
        elif any(word in command for word in ['joke', 'hasao', 'funny', 'mazak']):
            jokes = [
                "Ek aadmi doctor ke paas gaya. Doctor ne kaha - aapko sugar hai. Aadmi bola - haan, chai mein dalke peeta hun!",
                "Teacher ne student se pucha - Bharat mein kitne state hain? Student bola - Solid, liquid aur gas!",
                "Wife ne husband se kaha - aaj main bahut khush hun. Husband bola - kya baat hai? Wife boli - aaj tumne meri baat suni!"
            ]
            joke = random.choice(jokes)
            self.speak(joke)
            
        # Music command
        elif any(word in command for word in ['music', 'gaana', 'song', 'play']):
            self.speak("Music player khol raha hun. Spotify ya Apple Music use kariye.")
            subprocess.run(['open', '-a', 'Music'], check=False)
            
        # Calculator
        elif any(word in command for word in ['calculate', 'calculator', 'hisab', 'math']):
            self.speak("Calculator khol raha hun.")
            subprocess.run(['open', '-a', 'Calculator'], check=False)
            
        # Unknown command
        else:
            response = random.choice(self.unknown_responses)
            self.speak(response)
            
        return True  # Continue listening

    def start(self):
        """Start the voice assistant"""
        print("🎤 Hinglish Voice Assistant Starting...")
        print("Say 'Hello' to begin or 'Bye' to exit")
        
        # Initial greeting
        self.speak("Namaste! Main aapka Hinglish voice assistant hun. Hello boliye to start karenge.")
        
        self.is_listening = True
        
        while self.is_listening:
            try:
                command = self.listen()
                if command:
                    should_continue = self.process_command(command)
                    if not should_continue:
                        self.is_listening = False
                        break
                        
            except KeyboardInterrupt:
                print("\nBye bye! Assistant band kar raha hun.")
                self.speak("Alvida! Assistant band kar raha hun.")
                break
            except Exception as e:
                print(f"Error occurred: {e}")
                self.speak("Kuch gadbad hui hai. Phir se try kariye.")

if __name__ == "__main__":
    assistant = HinglishVoiceAssistant()
    assistant.start()
