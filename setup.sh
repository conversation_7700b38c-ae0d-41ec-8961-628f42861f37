#!/bin/bash

echo "🎤 Setting up Hinglish Voice Assistant for macOS..."

# Check if Python 3 is installed
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is not installed. Please install Python 3 first."
    exit 1
fi

# Check if pip is installed
if ! command -v pip3 &> /dev/null; then
    echo "❌ pip3 is not installed. Please install pip3 first."
    exit 1
fi

# Install portaudio (required for pyaudio on macOS)
echo "📦 Installing portaudio using Homebrew..."
if command -v brew &> /dev/null; then
    brew install portaudio
else
    echo "⚠️  Homebrew not found. Please install Homebrew first or install portaudio manually."
    echo "You can install Homebrew from: https://brew.sh/"
    exit 1
fi

# Install Python dependencies
echo "📦 Installing Python dependencies..."
pip3 install -r requirements.txt

echo "✅ Setup complete!"
echo ""
echo "🚀 To run the Hinglish Voice Assistant:"
echo "   python3 hinglish_voice_assistant.py"
echo ""
echo "📝 Note: Make sure to allow microphone access when prompted."
echo "🎯 Say 'Hello' to start and 'Bye' to exit."
