# 🎤 Hinglish Voice Assistant

A voice assistant that speaks in Hinglish (Hindi-English mix) designed for macOS. This assistant can understand English voice commands and responds in a natural Hinglish style.

## ✨ Features

- **Voice Recognition**: Understands English voice commands
- **Hinglish Responses**: Responds in natural Hindi-English mix
- **Built-in Commands**:
  - Greetings (Hello, Namaste)
  - Time queries
  - Weather information
  - Jokes in Hinglish
  - Open applications (Music, Calculator)
  - Goodbye commands

## 🚀 Quick Start

### Prerequisites
- macOS (uses built-in `say` command)
- Python 3.6+
- Homebrew (for installing dependencies)

### Installation

1. **Clone or download this project**
2. **Run the setup script**:
   ```bash
   chmod +x setup.sh
   ./setup.sh
   ```

3. **Run the assistant**:
   ```bash
   python3 hinglish_voice_assistant.py
   ```

### Manual Installation

If the setup script doesn't work, install manually:

```bash
# Install portaudio
brew install portaudio

# Install Python dependencies
pip3 install SpeechRecognition==3.10.0 pyaudio==0.2.11
```

## 🎯 Usage

1. **Start the assistant**: `python3 hinglish_voice_assistant.py`
2. **Say "Hello"** to begin interaction
3. **Try these commands**:
   - "Hello" or "Namaste" - Greeting
   - "What time is it?" - Get current time
   - "Tell me a joke" - Hear a Hinglish joke
   - "Play music" - Open Music app
   - "Calculator" - Open Calculator app
   - "Bye" or "Alvida" - Exit

## 🗣️ Sample Interactions

**You**: "Hello"
**Assistant**: "Namaste! Main aapka voice assistant hun. Kaise help kar sakta hun?"

**You**: "What time is it?"
**Assistant**: "Abhi time hai 2:30 PM"

**You**: "Tell me a joke"
**Assistant**: "Ek aadmi doctor ke paas gaya..."

## 🔧 Customization

You can easily customize the assistant by editing `hinglish_voice_assistant.py`:

- **Add new responses**: Modify the response lists (greetings, jokes, etc.)
- **Add new commands**: Add conditions in the `process_command` method
- **Change voice**: The assistant uses macOS built-in voices

## 🎵 Voice Settings

To change the voice used by macOS:
1. Go to **System Preferences** > **Accessibility** > **Spoken Content**
2. Choose a different voice from the dropdown
3. Some voices work better with Hinglish pronunciation

## 🐛 Troubleshooting

### Microphone Issues
- Make sure to allow microphone access when prompted
- Check **System Preferences** > **Security & Privacy** > **Microphone**

### Audio Issues
- Ensure your speakers/headphones are working
- Check volume settings

### Installation Issues
- Make sure Homebrew is installed: `/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"`
- Try installing portaudio manually: `brew install portaudio`

## 📝 Notes

- The assistant uses Google's speech recognition service (requires internet)
- Responses are in Hinglish but speech recognition is in English
- Works best in a quiet environment
- Say commands clearly for better recognition

## 🔮 Future Enhancements

- Add more Hinglish phrases and responses
- Integrate with more macOS applications
- Add weather API integration
- Support for Hindi voice commands
- Add conversation memory

## 📄 License

This project is open source. Feel free to modify and distribute.

---

**Enjoy your Hinglish voice assistant! 🎉**
